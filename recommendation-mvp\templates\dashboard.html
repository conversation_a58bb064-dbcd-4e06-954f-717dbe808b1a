{% extends "base.html" %}

{% block title %}Recommendation Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="/assets/styles.css">
<link rel="stylesheet" href="/assets/cluster-controls.css">
{% endblock %}

{% block content %}
<div class="dashboard-layout">
    <aside class="sidebar-filters">
        <div class="sidebar-section">
            <h2>Filters</h2>

            <div class="filter-group">
                <label>Update Frequency</label>
                <div class="frequency-tags">
                    <div class="frequency-tag all active" data-value="">All</div>
                    <div class="frequency-tag high" data-value="5.0,999">High</div>
                    <div class="frequency-tag medium" data-value="3.0,4.99">Medium</div>
                    <div class="frequency-tag occasional" data-value="1.0,2.99">Occasional</div>
                    <div class="frequency-tag low" data-value="0,0.99">Low</div>
                </div>
            </div>

            <div class="filter-group">
                <label for="appFilter">Application</label>
                <select id="appFilter" class="form-select" data-placeholder="Type to search application" style="width: 100%;">
                    <option value=""></option> {# Placeholder for Select2 #}
                </select>
            </div>

            <div class="filter-group">
                <label for="productFilter">Product</label>
                <select id="productFilter" class="form-select" data-placeholder="Type to search product" style="width: 100%;">
                    <option value=""></option> {# Placeholder for Select2 #}
                </select>
            </div>

            <div class="filter-group">
                <label for="urgencyFilter">Urgency</label>
                <select id="urgencyFilter" class="form-select" data-placeholder="Select urgency" style="width: 100%;">
                    <option value="">All Urgencies</option>
                    <option value="high">High Urgency</option>
                    <option value="medium">Medium Urgency</option>
                    <option value="low">Low Urgency</option>
                </select>
            </div>

            <div class="filter-group sort-options">
                <label>Sort By</label>
                <div class="sort-selects">
                    <select id="sortBySelect" class="form-select">
                        <option value="priority">Priority Score</option>
                        <option value="app_count">App Count</option>
                        <option value="server_count">Server Count</option>
                        <option value="owner_name">Owner Name</option>
                    </select>
                    <select id="sortOrderSelect" class="form-select">
                        <option value="desc">Descending</option>
                        <option value="asc">Ascending</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="sidebar-section">
            <h2>L-Level Filters</h2>
            <p class="filter-note">Filter applications based on organizational structure.</p>

            <div class="filter-group">
                <label for="appOwnerSearch">App Owner Search</label>
                <input type="text" id="appOwnerSearch" class="form-control" placeholder="Type owner name...">
            </div>

            <div id="lLevelFilters">
                <p style="font-size: 0.85em; color: #777;">Loading L-Level Filters...</p>
            </div>
             <div class="l-level-actions">
                 <button type="button" id="applyLLevelFilters" class="dell-button apply-l-level">
                     Apply L-Level Filters
                 </button>
                 <button type="button" id="clearLLevelFilters" class="dell-button secondary small">
                     Clear L-Level
                 </button>
             </div>
        </div>

        <div class="filters-actions">
             <button type="button" class="dell-button clear-filters secondary">
                 <i class="fas fa-filter-circle-xmark" style="margin-right: 5px;"></i>
                 Clear All Filters
             </button>
        </div>
    </aside>

    <main class="main-content">
        <div class="analysis-tabs">
            <div class="tab-buttons">
                <button class="tab-button active" data-tab="summary">Summary</button>
                <button class="tab-button" data-tab="org-funnel">Org Funnel</button>
                <button class="tab-button" data-tab="product-rankings">Product Rankings</button>
                <button class="tab-button" data-tab="enhanced-correlation">Enhanced Correlation</button>
            </div>

            <div class="tab-content">
                <div id="summary-tab" class="tab-pane active">
                    <div class="summary-section">
                        <h2>General Summary</h2>
                        <div class="summary-grid">
                            <div class="summary-item">
                                <strong>Total Owners:</strong> <span id="summaryTotalOwners">0</span>
                            </div>
                            <div class="summary-item">
                                <strong>Total Apps:</strong> <span id="summaryTotalApps">0</span>
                            </div>
                            <div class="summary-item">
                                <strong>Total Servers:</strong> <span id="summaryTotalServers">0</span>
                            </div>
                            <div class="summary-item">
                                <strong>Environments:</strong><br>
                                <span id="summaryEnvironments">N/A</span>
                            </div>
                            <div class="summary-item">
                                <strong>Most Common Products (Top 5):</strong><br>
                                <span id="summaryProducts">N/A</span>
                            </div>
                        </div>

                        <div class="remediation-stats-section">
                            <h3>Remediation Statistics</h3>
                            <div class="remediation-grid">
                                <div class="remediation-item">
                                    <strong>Total Items:</strong> <span id="remediationTotalItems">0</span>
                                </div>
                                <div class="remediation-item">
                                    <strong>Remediated:</strong> <span id="remediationRemediated">0</span>
                                </div>
                                <div class="remediation-item">
                                    <strong>Pending:</strong> <span id="remediationPending">0</span>
                                </div>
                                <div class="remediation-item">
                                    <strong>Success Rate:</strong> <span id="remediationSuccessRate">0%</span>
                                </div>
                                <div class="remediation-item">
                                    <strong>Failed:</strong> <span id="remediationFailed">0</span>
                                </div>
                                <div class="remediation-item">
                                    <strong>Unknown:</strong> <span id="remediationUnknown">0</span>
                                </div>
                            </div>
                        </div>

                        <div class="top-applications-section">
                            <h3>Top 5 Applications by Remediation Activity</h3>
                            <div id="topApplicationsList" class="top-applications-list">
                                <div class="loading-placeholder">Loading top applications...</div>
                            </div>
                        </div>

                        <div class="intelligent-insights-section">
                            <h3>Intelligent Insights</h3>
                            <div id="intelligentInsights" class="intelligent-insights-container">
                                <div class="loading-placeholder">Analyzing patterns...</div>
                            </div>
                        </div>

                        <div class="strategic-actions-section">
                            <h3>🎯 Strategic Actions - Quick Wins</h3>
                            <p class="section-subtitle">Direct recommendations for immediate impact and visibility</p>
                            <div id="strategicActions" class="strategic-actions-container">
                                <div class="loading-placeholder">Calculating strategic moves...</div>
                            </div>
                        </div>

                        <div class="charts-row">
                            <div class="chart-section">
                                <h2>Client Execution Analysis</h2>
                                <div class="chart-container">
                                    <canvas id="executionChart"></canvas>
                                </div>
                            </div>
                            <div class="chart-section">
                                <h2>Application Execution Analysis</h2>
                                <div class="chart-container">
                                    <canvas id="appExecutionChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                     <div class="applications-section">
                         <h2>Applications</h2>
                         <div class="pagination-controls pagination-controls-top">
                             <button id="prevPage" class="pagination-button" disabled>Previous</button>
                             <span class="pagination-info">Page <span id="currentPage">1</span> of <span id="totalPages">1</span></span>
                             <button id="nextPage" class="pagination-button">Next</button>
                         </div>

                         <div id="applicationsContainer" class="applications-container">
                             <div class="loading-placeholder">Loading applications...</div>
                         </div>

                         <div class="pagination-controls pagination-controls-bottom">
                             <button id="prevPageBottom" class="pagination-button" disabled>Previous</button>
                             <span class="pagination-info">Page <span id="currentPageBottom">1</span> of <span id="totalPagesBottom">1</span></span>
                             <button id="nextPageBottom" class="pagination-button">Next</button>
                         </div>
                     </div>
                </div> <div id="org-funnel-tab" class="tab-pane">
                    <div class="loading-placeholder">Loading Organizational Funnel...</div>
                </div> <div id="product-rankings-tab" class="tab-pane">
                     <div class="product-clustering-section">
                         <h2>Product Clustering & Correlation (App Owner: <span id="clusterOwnerName">All</span>)</h2>
                         <p>Visualize relationships between products for the selected app owner to identify remediation opportunities.</p>
                         <div id="clusterVisualizationContainer" class="cluster-visualization-container">
                             <div class="loading-placeholder">Loading cluster visualization...</div>
                         </div>
                     </div>
                </div> <div id="enhanced-correlation-tab" class="tab-pane">
                     <div class="loading-placeholder">Select the Enhanced Correlation tab to load visualization.</div>
                </div> </div> </div> </main> </div> <div id="loadingOverlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 10000; justify-content: center; align-items: center;">
     <div class="loading-spinner" style="border-top-color: #fff;"></div>
     <span style="color: white; margin-left: 10px;">Loading...</span>
</div>

{% endblock %}

{% block scripts %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://d3js.org/d3.v7.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="/assets/dashboard.js"></script>
{% endblock %}
