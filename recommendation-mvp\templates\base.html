<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recommendation Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    {% block extra_css %}{% endblock %}
    <!-- Load our custom CSS AFTER Bootstrap to ensure higher priority -->
    <link rel="stylesheet" href="/assets/styles.css">

    <!-- INLINE Dell Header CSS to ensure it loads properly -->
    <style>
        /* Dell Header - EXACT Reference Image Match - INLINE STYLES */
        .dell-header-exact {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            width: 100% !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            position: relative !important;
            z-index: 1000 !important;
            margin: 0 !important;
            padding: 0 !important;
            display: block !important;
        }

        /* Universal container for all header sections */
        .dell-container {
            width: 100% !important;
            max-width: none !important;
            padding: 0 20px !important;
            margin: 0 !important;
            box-sizing: border-box !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
        }

        /* Top blue bar - EXACT reference colors and spacing */
        .dell-top-bar {
            background: linear-gradient(to bottom, #4A90E2 0%, #357ABD 100%) !important;
            color: white !important;
            padding: 4px 0 !important;
            font-size: 12px !important;
            border-bottom: 1px solid #2E5F8A !important;
            width: 100% !important;
        }

        .dell-brand-text {
            font-weight: 600 !important;
            font-size: 12px !important;
            color: white !important;
        }

        .dell-top-links {
            display: flex !important;
            gap: 20px !important;
        }

        .dell-top-link {
            color: white !important;
            text-decoration: none !important;
            font-size: 12px !important;
            font-weight: 400 !important;
            transition: opacity 0.2s !important;
        }

        .dell-top-link:hover {
            opacity: 0.9 !important;
            color: white !important;
        }

        /* Main navigation bar - EXACT reference blue and layout */
        .dell-main-nav {
            background: linear-gradient(to bottom, #5BA0F2 0%, #4A90E2 100%) !important;
            color: white !important;
            padding: 8px 0 !important;
            border-bottom: 1px solid #357ABD !important;
            width: 100% !important;
        }

        .dell-main-left {
            display: flex !important;
            align-items: center !important;
            gap: 30px !important;
        }

        .dell-main-right {
            display: flex !important;
            align-items: center !important;
            gap: 18px !important;
        }

        .dell-main-brand {
            font-size: 20px !important;
            font-weight: 600 !important;
            color: white !important;
            white-space: nowrap !important;
        }

        .dell-search-wrapper {
            display: flex !important;
            align-items: center !important;
            background: white !important;
            border-radius: 3px !important;
            border: 1px solid #ccc !important;
            box-shadow: inset 0 1px 2px rgba(0,0,0,0.1) !important;
        }

        .dell-search-field {
            border: none !important;
            padding: 7px 12px !important;
            font-size: 13px !important;
            outline: none !important;
            width: 260px !important;
            border-radius: 3px 0 0 3px !important;
            background: white !important;
        }

        .dell-search-button {
            background: #f8f9fa !important;
            border: none !important;
            border-left: 1px solid #ddd !important;
            padding: 7px 12px !important;
            cursor: pointer !important;
            font-size: 13px !important;
            border-radius: 0 3px 3px 0 !important;
            color: #666 !important;
        }

        .dell-main-link {
            color: white !important;
            text-decoration: none !important;
            font-size: 13px !important;
            font-weight: 400 !important;
            transition: opacity 0.2s !important;
            white-space: nowrap !important;
        }

        .dell-main-link:hover {
            opacity: 0.9 !important;
            color: white !important;
        }

        .dell-user-circle {
            background: rgba(255,255,255,0.2) !important;
            border-radius: 50% !important;
            width: 28px !important;
            height: 28px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 14px !important;
            color: white !important;
        }

        .dell-notification {
            font-size: 8px !important;
            vertical-align: super !important;
        }

        /* Sub navigation bar - EXACT reference styling */
        .dell-sub-bar {
            background: linear-gradient(to bottom, #6BB0FF 0%, #5BA0F2 100%) !important;
            color: white !important;
            padding: 6px 0 !important;
            border-bottom: 1px solid #4A90E2 !important;
            width: 100% !important;
        }

        .dell-sub-links {
            display: flex !important;
            gap: 30px !important;
        }

        .dell-sub-link {
            color: white !important;
            text-decoration: none !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            transition: opacity 0.2s !important;
        }

        .dell-sub-link:hover {
            opacity: 0.9 !important;
            color: white !important;
        }

        /* Breadcrumb bar - EXACT reference styling */
        .dell-breadcrumb-bar {
            background: #f8f9fa !important;
            padding: 6px 0 !important;
            border-bottom: 1px solid #e9ecef !important;
            width: 100% !important;
        }

        .dell-breadcrumb-nav {
            display: flex !important;
            align-items: center !important;
            font-size: 13px !important;
        }

        .dell-breadcrumb-link {
            color: #007bff !important;
            text-decoration: none !important;
            font-weight: 400 !important;
        }

        .dell-breadcrumb-link:hover {
            text-decoration: underline !important;
        }

        .dell-breadcrumb-sep {
            color: #6c757d !important;
            margin: 0 4px !important;
        }

        .dell-breadcrumb-current {
            color: #495057 !important;
            font-weight: 500 !important;
        }
    </style>
</head>
<body>
    <header class="dell-header-exact">
        <!-- Top blue bar - EXACT reference match -->
        <div class="dell-top-bar">
            <div class="dell-container">
                <span class="dell-brand-text">Inside Dell Technologies</span>
                <div class="dell-top-links">
                    <a href="#" class="dell-top-link">Learning Central</a>
                    <a href="#" class="dell-top-link">Dell Culture ▼</a>
                    <a href="#" class="dell-top-link">Products & Offerings ▼</a>
                    <a href="#" class="dell-top-link">Organizations ▼</a>
                    <a href="#" class="dell-top-link">Quick Links ▼</a>
                    <a href="#" class="dell-top-link">Resource Hub</a>
                </div>
            </div>
        </div>

        <!-- Main Team Member Center bar - EXACT reference match -->
        <div class="dell-main-nav">
            <div class="dell-container">
                <div class="dell-main-left">
                    <span class="dell-main-brand">Team Member Center</span>
                    <div class="dell-search-wrapper">
                        <input type="text" class="dell-search-field" placeholder="Search Team Member Center">
                        <button class="dell-search-button">🔍</button>
                    </div>
                </div>
                <div class="dell-main-right">
                    <a href="#" class="dell-main-link">My Tasks</a>
                    <a href="#" class="dell-main-link">My Requests</a>
                    <a href="#" class="dell-main-link">My Favorites</a>
                    <div class="dell-user-circle">👤</div>
                    <a href="#" class="dell-main-link">🛒 Cart</a>
                    <a href="#" class="dell-main-link">Tours <span class="dell-notification">🔴</span></a>
                </div>
            </div>
        </div>

        <!-- Sub navigation - EXACT reference match -->
        <div class="dell-sub-bar">
            <div class="dell-container">
                <div class="dell-sub-links">
                    <a href="#" class="dell-sub-link">My HR ▼</a>
                    <a href="#" class="dell-sub-link">My IT ▼</a>
                    <a href="#" class="dell-sub-link">My Security ▼</a>
                    <a href="#" class="dell-sub-link">My Legal ▼</a>
                    <a href="#" class="dell-sub-link">My Facilities ▼</a>
                </div>
            </div>
        </div>

        <!-- Breadcrumb - EXACT reference match -->
        <div class="dell-breadcrumb-bar">
            <div class="dell-container">
                <div class="dell-breadcrumb-nav">
                    <a href="#" class="dell-breadcrumb-link">Team Member Center</a>
                    <span class="dell-breadcrumb-sep"> / </span>
                    <span class="dell-breadcrumb-current">My IT</span>
                </div>
            </div>
        </div>
    </header>

    <main class="main-container">
        {% block content %}{% endblock %}
    </main>

    <footer class="footer mt-auto py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">&copy; 2025 Recommendation System</span>
        </div>
    </footer>
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
