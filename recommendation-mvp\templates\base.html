<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recommendation Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    {% block extra_css %}{% endblock %}
    <!-- Load our custom CSS AFTER Bootstrap to ensure higher priority -->
    <link rel="stylesheet" href="/assets/styles.css">

    <!-- Minimalistic Dell Header CSS -->
    <style>
        .dell-header {
            background: #0076CE !important;
            padding: 12px 0 !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        .dell-container {
            max-width: none !important;
            padding: 0 20px !important;
            margin: 0 !important;
        }

        .dell-logo {
            height: 40px !important;
            width: 40px !important;
        }

        .dell-title {
            color: white !important;
            font-size: 24px !important;
            font-weight: 600 !important;
            margin: 0 !important;
        }
    </style>
</head>
<body>
    <header class="dell-header sticky-top">
        <nav class="navbar navbar-expand-lg">
            <div class="container-xxl dell-container">
                <div class="row align-items-center w-100">
                    <div class="col-auto">
                        <div class="dell-logo d-inline-block align-text-top">
                            <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="40" height="40" rx="4" fill="white"/>
                                <text x="20" y="26" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0076CE" text-anchor="middle">DELL</text>
                            </svg>
                        </div>
                    </div>
                    <div class="col-auto">
                        <h3 class="dell-title mb-0">Recommendation Dashboard</h3>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-container">
        {% block content %}{% endblock %}
    </main>

    <footer class="footer mt-auto py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">&copy; 2025 Recommendation System</span>
        </div>
    </footer>
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
