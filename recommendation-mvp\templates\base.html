<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recommendation Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    {% block extra_css %}{% endblock %}
    <!-- Load our custom CSS AFTER Bootstrap to ensure higher priority -->
    <link rel="stylesheet" href="/assets/styles.css">

    <!-- Minimalistic Dell Header CSS -->
    <style>
        .dell-header {
            background: #0076CE !important;
            padding: 12px 0 !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        }

        .dell-container {
            max-width: none !important;
            padding: 0 20px !important;
            margin: 0 !important;
        }

        .dell-logo {
            height: 40px !important;
            width: 40px !important;
        }

        .dell-title {
            color: white !important;
            font-size: 24px !important;
            font-weight: 600 !important;
            margin: 0 !important;
        }
    </style>
</head>
<body>
    <header class="dell-header sticky-top">
        <nav class="navbar navbar-expand-lg">
            <div class="container-xxl dell-container">
                <div class="row align-items-center w-100">
                    <div class="col-auto">
                        <div class="dell-logo d-inline-block align-text-top">
                            <svg xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg" version="1.1" id="svg3794" viewBox="0 0 300 300" height="40" width="40">
                              <defs id="defs3796"/>
                              <metadata id="metadata3799">
                                <rdf:RDF>
                                  <cc:Work rdf:about="">
                                    <dc:format>image/svg+xml</dc:format>
                                    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
                                    <dc:title/>
                                  </cc:Work>
                                </rdf:RDF>
                              </metadata>
                              <g transform="translate(-318.33375,-439.74274)" id="layer1">
                                <g transform="matrix(4.579965,0,0,-4.579965,468.34291,456.8459)" id="g3460">
                                  <path id="path3462" style="fill:#007db8;fill-opacity:1;fill-rule:nonzero;stroke:none" d="m 0,0 c -8.01,0 -15.264,-3.249 -20.516,-8.505 -5.254,-5.244 -8.501,-12.502 -8.501,-20.516 0,-8.008 3.247,-15.261 8.501,-20.507 5.252,-5.249 12.506,-8.504 20.516,-8.504 8.012,0 15.27,3.255 20.514,8.504 5.252,5.246 8.492,12.499 8.492,20.507 0,8.014 -3.24,15.272 -8.492,20.516 C 15.27,-3.249 8.012,0 0,0 m 0,3.516 c 17.965,0 32.531,-14.568 32.531,-32.537 0,-17.963 -14.566,-32.529 -32.531,-32.529 -17.963,0 -32.535,14.566 -32.535,32.529 0,17.969 14.572,32.537 32.535,32.537"/>
                                </g>
                                <g transform="matrix(4.579965,0,0,-4.579965,397.87238,588.54693)" id="g3464">
                                  <path id="path3466" style="fill:#007db8;fill-opacity:1;fill-rule:nonzero;stroke:none" d="m 0,0 c 0,1.896 -1.258,2.973 -3.039,2.973 l -1.09,0 0,-5.948 1.059,0 C -1.414,-2.975 0,-2.075 0,0 M 19.389,-2.14 11.359,-8.463 4.02,-2.685 C 2.961,-5.229 0.402,-6.996 -2.545,-6.996 l -6.281,0 0,13.992 6.281,0 c 3.293,0 5.666,-2.094 6.563,-4.325 l 7.341,5.772 2.719,-2.14 -6.728,-5.288 1.293,-1.012 6.726,5.285 2.723,-2.134 -6.727,-5.294 1.291,-1.014 6.733,5.295 0,4.855 4.881,0 0,-9.908 4.869,0 0,-4.101 -9.75,0 0,4.873 z m 15.933,-0.774 4.867,0 0,-4.099 -9.753,0 0,14.009 4.886,0 0,-9.91 z"/>
                                </g>
                              </g>
                            </svg>
                        </div>
                    </div>
                    <div class="col-auto">
                        <h3 class="dell-title mb-0">Recommendation Dashboard</h3>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-container">
        {% block content %}{% endblock %}
    </main>

    <footer class="footer mt-auto py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">&copy; 2025 Recommendation System</span>
        </div>
    </footer>
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
